import { useRef, useEffect, useCallback } from 'react';

interface UseStreamingViewportProps {
  scrollContainerRef: React.RefObject<HTMLDivElement | null>;
  isStreaming: boolean;
  onStreamingStart?: () => void;
  onStreamingEnd?: () => void;
}

interface UseStreamingViewportReturn {
  isUserAtBottom: boolean;
  scrollToBottom: () => void;
  preserveScrollPosition: () => void;
  getScrollInfo: () => {
    scrollTop: number;
    scrollHeight: number;
    clientHeight: number;
    isAtBottom: boolean;
  };
}


const useStreamingViewport = ({
  scrollContainerRef,
  isStreaming,
  onStreamingStart,
  onStreamingEnd
}: UseStreamingViewportProps): UseStreamingViewportReturn => {
  const preservedScrollPosition = useRef<number | null>(null);
  const wasStreamingRef = useRef(false);
  const isUserAtBottomRef = useRef(false);

  const checkIfAtBottom = useCallback((): boolean => {
    const container = scrollContainerRef.current;
    if (!container) return false;

    const threshold = 10;
    const isAtBottom = container.scrollHeight - container.scrollTop - container.clientHeight <= threshold;
    isUserAtBottomRef.current = isAtBottom;
    return isAtBottom;
  }, [scrollContainerRef]);

  const scrollToBottom = useCallback(() => {
    const container = scrollContainerRef.current;
    if (container) {
      container.scrollTop = container.scrollHeight;
      isUserAtBottomRef.current = true;
    }
  }, [scrollContainerRef]);

  const preserveScrollPosition = useCallback(() => {
    const container = scrollContainerRef.current;
    if (container) {
      preservedScrollPosition.current = container.scrollTop;
    }
  }, [scrollContainerRef]);

  const getScrollInfo = useCallback(() => {
    const container = scrollContainerRef.current;
    if (!container) {
      return {
        scrollTop: 0,
        scrollHeight: 0,
        clientHeight: 0,
        isAtBottom: false
      };
    }

    return {
      scrollTop: container.scrollTop,
      scrollHeight: container.scrollHeight,
      clientHeight: container.clientHeight,
      isAtBottom: checkIfAtBottom()
    };
  }, [scrollContainerRef, checkIfAtBottom]);

  useEffect(() => {
    if (isStreaming && !wasStreamingRef.current) {
      preserveScrollPosition();
      checkIfAtBottom();
      onStreamingStart?.();
      wasStreamingRef.current = true;
    } else if (!isStreaming && wasStreamingRef.current) {
      onStreamingEnd?.();
      wasStreamingRef.current = false;
      preservedScrollPosition.current = null;
    }
  }, [isStreaming, preserveScrollPosition, checkIfAtBottom, onStreamingStart, onStreamingEnd]);

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      checkIfAtBottom();
    };

    container.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      container.removeEventListener('scroll', handleScroll);
    };
  }, [scrollContainerRef, checkIfAtBottom]);

  return {
    isUserAtBottom: isUserAtBottomRef.current,
    scrollToBottom,
    preserveScrollPosition,
    getScrollInfo
  };
};

export default useStreamingViewport;
