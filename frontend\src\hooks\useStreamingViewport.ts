import { useRef, useEffect, useCallback } from 'react';

interface UseStreamingViewportProps {
  scrollContainerRef: React.RefObject<HTMLDivElement | null>;
  isStreaming: boolean;
  onStreamingStart?: () => void;
  onStreamingEnd?: () => void;
}

interface UseStreamingViewportReturn {
  isUserAtBottom: boolean;
  scrollToBottom: () => void;
  preserveScrollPosition: () => void;
  getScrollInfo: () => {
    scrollTop: number;
    scrollHeight: number;
    clientHeight: number;
    isAtBottom: boolean;
  };
}

/**
 * Hook for managing viewport behavior during streaming content.
 * Provides Gemini/Mistral-like experience where viewport doesn't auto-scroll
 * but scrollbar adjusts dynamically to show content growth.
 */
const useStreamingViewport = ({
  scrollContainerRef,
  isStreaming,
  onStreamingStart,
  onStreamingEnd
}: UseStreamingViewportProps): UseStreamingViewportReturn => {
  const preservedScrollPosition = useRef<number | null>(null);
  const wasStreamingRef = useRef(false);
  const isUserAtBottomRef = useRef(false);

  // Check if user is at the bottom of the scroll container
  const checkIfAtBottom = useCallback((): boolean => {
    const container = scrollContainerRef.current;
    if (!container) return false;
    
    const threshold = 10; // 10px threshold for "at bottom"
    const isAtBottom = container.scrollHeight - container.scrollTop - container.clientHeight <= threshold;
    isUserAtBottomRef.current = isAtBottom;
    return isAtBottom;
  }, [scrollContainerRef]);

  // Scroll to bottom of container
  const scrollToBottom = useCallback(() => {
    const container = scrollContainerRef.current;
    if (container) {
      container.scrollTop = container.scrollHeight;
      isUserAtBottomRef.current = true;
    }
  }, [scrollContainerRef]);

  // Preserve current scroll position
  const preserveScrollPosition = useCallback(() => {
    const container = scrollContainerRef.current;
    if (container) {
      preservedScrollPosition.current = container.scrollTop;
    }
  }, [scrollContainerRef]);

  // Get detailed scroll information
  const getScrollInfo = useCallback(() => {
    const container = scrollContainerRef.current;
    if (!container) {
      return {
        scrollTop: 0,
        scrollHeight: 0,
        clientHeight: 0,
        isAtBottom: false
      };
    }

    return {
      scrollTop: container.scrollTop,
      scrollHeight: container.scrollHeight,
      clientHeight: container.clientHeight,
      isAtBottom: checkIfAtBottom()
    };
  }, [scrollContainerRef, checkIfAtBottom]);

  // Handle streaming state changes
  useEffect(() => {
    if (isStreaming && !wasStreamingRef.current) {
      // Streaming started
      preserveScrollPosition();
      checkIfAtBottom();
      onStreamingStart?.();
      wasStreamingRef.current = true;
    } else if (!isStreaming && wasStreamingRef.current) {
      // Streaming ended
      onStreamingEnd?.();
      wasStreamingRef.current = false;
      preservedScrollPosition.current = null;
    }
  }, [isStreaming, preserveScrollPosition, checkIfAtBottom, onStreamingStart, onStreamingEnd]);

  // Set up scroll event listener to track user scroll behavior
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      checkIfAtBottom();
    };

    container.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      container.removeEventListener('scroll', handleScroll);
    };
  }, [scrollContainerRef, checkIfAtBottom]);

  return {
    isUserAtBottom: isUserAtBottomRef.current,
    scrollToBottom,
    preserveScrollPosition,
    getScrollInfo
  };
};

export default useStreamingViewport;
