import React from 'react';
import { Message, FilterState } from '../../types/ConversationTypes';

interface LayoutContextProps {
  centeredContent: boolean;
  updateCenteredContentState: (value: boolean) => void;
  errorMessage: string | null;
  updateErrorMessageState: (value: string | null) => void;
  refreshChatHistory: boolean;
  updateRefreshChatHistory: (value: boolean) => void;
  conversationId: string | '';
  updateConversationId: (value: string | '') => void;
  appLoader: boolean;
  updateAppLoaderState: (value: boolean) => void;
  isStreamingContent: boolean;
  updateIsStreamingContent: (value: boolean) => void;
  activeMessageInfo: Message | null;
  updateActiveMessageInfo: (value: Message | null) => void;
  messageList: Message[];
  updateMessageList: (value: Message[]) => void;
  activeMessageStreamingEnded: boolean;
  updateActiveMessageStreamingEnded: (value: boolean) => void;
  streamingError: boolean;
  updateStreamingErrorState: (value: boolean) => void;
  isSidebarCollapsed: boolean;
  toggleSidebarCollapsed: (isUserExplicitAction?: boolean) => void;
  setIsSidebarCollapsed: (value: boolean) => void;
  userExpandedSidebar: boolean;
  setUserExpandedSidebar: (value: boolean) => void;
  activeFilters: FilterState | null;
  updateActiveFilters: (filters: FilterState | null) => void;
}

export const LayoutContext = React.createContext<LayoutContextProps>({
  centeredContent: false,
  updateCenteredContentState: () => { },
  errorMessage: null,
  updateErrorMessageState: () => { },
  refreshChatHistory: false,
  updateRefreshChatHistory: () => { },
  conversationId: '',
  updateConversationId: () => { },
  appLoader: false,
  updateAppLoaderState: () => { },
  isStreamingContent: false,
  updateIsStreamingContent: () => { },
  activeMessageInfo: null,
  updateActiveMessageInfo: () => { },
  messageList: [],
  updateMessageList: () => { },
  activeMessageStreamingEnded: false,
  updateActiveMessageStreamingEnded: () => { },
  streamingError: false,
  updateStreamingErrorState: () => { },
  isSidebarCollapsed: false,
  toggleSidebarCollapsed: () => { },
  setIsSidebarCollapsed: () => { },
  userExpandedSidebar: false,
  setUserExpandedSidebar: () => { },
  activeFilters: null,
  updateActiveFilters: () => { },
});

export const LayoutProvider = LayoutContext.Provider;
export const LayoutConsumer = LayoutContext.Consumer;