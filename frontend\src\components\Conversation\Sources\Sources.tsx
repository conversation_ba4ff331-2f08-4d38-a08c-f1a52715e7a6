import React, { useEffect, useState, useCallback, useRef, useMemo } from "react";
import {
  Box,
  Card,
  Typography,
  IconButton,
  useTheme,
  alpha,
  Grid,
} from "@mui/material";
import CloseIcon from '@mui/icons-material/Close';
import { AnimatePresence, motion } from "framer-motion";
import { Source, FilterState } from "../../../types/ConversationTypes";
import FilterListAltIcon from '@mui/icons-material/FilterListAlt';
import SourceCard from './SourceCard';
import FiltersPanel from '../FilterPanel/FiltersPanel';
import FilterHeader from '../FilterPanel/FilterHeader';
import SourcesSkeleton from '../../Common/FiltersSkeleton/SourcesSkeleton';
import { getYearFromCitation } from './utils/SourceUtils';

interface SourcesProps {
  onClose: () => void;
  sources: Source[] | undefined | null;
  messageId: string | null;
  selectedStudy: string;
  activeSourcePaperIds: string[];
  activeSourceMessageId: string | null;
  displayCloseButton?: boolean;
  onClearFilter?: () => void;
  hideSourceCount?: boolean;
  expandable?: boolean;
  alwaysExpanded?: boolean;
  hideArticleIcon?: boolean;
  maxAbstractLines?: number;
  disableSelectedBorder?: boolean;
  disableAutoScroll?: boolean;
  disableSelectedHighlight?: boolean;
  iconSpacing?: number;
  showCloseButton?: boolean;
  onCloseSource?: (sourceId: string) => void;
  interventionDetails?: {
    name: string;
    description: string;
  };
  outcomeDetails?: {
    name: string;
    description: string;
  };
  hideBorder?: boolean;
  onFiltersChange: (filters: {
    year: number[],
    sectors: string[],
    geographies: string[],
  }) => void;
  onResetFilters: () => void;
  filterOptions: FilterState;
  activeFilters: any;
  mode: 'standalone' | 'comparison';
}

const Sources: React.FC<SourcesProps> = ({
  onClose,
  sources,
  messageId,
  selectedStudy,
  activeSourcePaperIds,
  activeSourceMessageId,
  displayCloseButton = true,
  onClearFilter,
  hideSourceCount = false,
  expandable = false,
  alwaysExpanded = false,
  hideArticleIcon = false,
  maxAbstractLines = 3,
  disableSelectedBorder = false,
  disableAutoScroll = false,
  disableSelectedHighlight = false,
  iconSpacing = 1,
  showCloseButton = false,
  onCloseSource,
  interventionDetails,
  outcomeDetails,
  hideBorder = false,
  onFiltersChange,
  onResetFilters,
  filterOptions,
  activeFilters,
  mode
}) => {
  const [currentSources, setCurrentSources] = useState<Source[]>([]);
  const [loading, setLoading] = useState(true);
  const [, setError] = useState<string | null>(null);
  const [expandedSources, setExpandedSources] = useState<Set<string>>(new Set());
  const isFiltered = messageId === activeSourcePaperIds && activeSourcePaperIds.length > 0;
  const theme = useTheme();
  const cardRefs = useRef<(HTMLDivElement | null)[]>([]);
  const [showFiltersPanel, setShowFiltersPanel] = useState(mode === 'standalone');

  const activeFiltersCount = useMemo(() => {
    if (mode === 'comparison') {
      return 0;
    }
    return Object.values(activeFilters || {}).filter(val => Array.isArray(val) && val.length > 0).length;
  }, [activeFilters, mode]);

  const [skeletonLoadingTimer, setSkeletonLoadingTimer] = useState(0);

  const handleClearFilters = () => {
    onResetFilters();
  };

  const handleToggleFiltersPanel = () => {
    setShowFiltersPanel(prev => !prev);
  };

  const toggleSourceExpansion = useCallback((sourceId: string) => {
    if (!expandable) return;
    setExpandedSources(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sourceId)) {
        newSet.delete(sourceId);
      } else {
        newSet.add(sourceId);
      }
      return newSet;
    });
  }, [expandable]);

  const isSourceExpanded = useCallback((sourceId: string) => {
    return expandedSources.has(sourceId);
  }, [expandedSources]);

  const handleShowAll = () => {
    onClearFilter && onClearFilter();
  };

  useEffect(() => {
    if (messageId && sources === undefined) {
      setLoading(true);
    }
  }, [messageId, sources]);

  useEffect(() => {
    // Only set skeleton timer in standalone mode
    if (mode === 'standalone') {
      setSkeletonLoadingTimer(500);
    }
  }, [mode]);

  useEffect(() => {
    if (messageId && sources !== undefined) {
      const loadingTimer = setTimeout(() => {
        if (sources) {
          let validSources = sources.filter(
            (item) =>
              (typeof item.citation === 'string' && item.citation.trim()) ||
              (typeof item.title === 'string' && item.title.trim())
          ).sort((b, a) => {
            if (a.position && b.position) {
              return b.position - a.position;
            }
            return 1;
          });

          // Only apply filters if in standalone mode
          if (mode === 'standalone') {
            const fullYearRange = filterOptions?.years;
            const currentYearRange = activeFilters?.years;

            if (currentYearRange && fullYearRange && (currentYearRange[0] !== fullYearRange[0] || currentYearRange[1] !== fullYearRange[1])) {
              const [minYear, maxYear] = currentYearRange;
              validSources = validSources.filter(source => {
                const sourceYear = getYearFromCitation(source.citation);
                return sourceYear !== null && sourceYear >= minYear && sourceYear <= maxYear;
              });
            }

            if (activeFilters?.sectors?.length > 0) {
              validSources = validSources.filter(source =>
                source.sector && source.sector.split(';').some(sector => activeFilters.sectors.includes(sector.trim()))
              );
            }

            if (activeFilters?.countries?.length > 0) {
              validSources = validSources.filter(source =>
                source.country && activeFilters.countries.includes(source.country)
              );
            }
          }

          if (isFiltered && activeSourcePaperIds.length > 0) {
            validSources = validSources.filter(source =>
              source.paper_id && activeSourcePaperIds.includes(source.short_paper_id)
            );
          }

          // preserve previous list if new filters yield no results
          if (validSources.length > 0 || activeFiltersCount === 0) {
            setCurrentSources(validSources);
            setError(null);
          } else {
            setError("No sources match this filter combination. The previous list is shown.");
          }
          setLoading(false);

          if (!disableAutoScroll && !hideSourceCount && selectedStudy) {
            const selectedIndex = validSources.findIndex(
              (item) => String(item.short_paper_id) === String(selectedStudy)
            );
            if (selectedIndex !== -1) {
              setTimeout(() => {
                if (cardRefs.current[selectedIndex]) {
                  cardRefs.current[selectedIndex]?.scrollIntoView({
                    behavior: "smooth",
                    block: "center",
                  });
                }
              }, 0);
            }
          }
        } else if (sources === null) {
          setLoading(false);
          setError('No sources available for this message.');
        }
      }, skeletonLoadingTimer);

      return () => clearTimeout(loadingTimer);
    } else {
      setCurrentSources([]);
      setLoading(false);
      setError(null);
    }
  }, [messageId, sources, selectedStudy, isFiltered, activeSourcePaperIds, disableAutoScroll, hideSourceCount, activeFilters, filterOptions, skeletonLoadingTimer, mode, activeFiltersCount]);

  const setCardRef = useCallback((el: HTMLDivElement | null, index: number) => {
    if (el) {
      cardRefs.current[index] = el;
    }
  }, []);

  // Use mode to determine whether to show the skeleton
  if (loading && mode === 'standalone') {
    return <SourcesSkeleton hideFiltersPanel={!showFiltersPanel} onClose={onClose} theme={theme} />;
  }

  // Fallback if no sources are available
  if (!currentSources || currentSources.length === 0) return null;

  const renderSourceList = () => (
    <AnimatePresence initial={false}>
      {currentSources.map((item, index) => (
        <motion.div
          key={item.id}
          style={{
            overflow: 'hidden',
            marginBottom: theme.spacing(1.5),
          }}
          ref={(el) => setCardRef(el, index)}
        >
          <SourceCard
            source={item}
            isSelected={selectedStudy === item.short_paper_id}
            isExpanded={isSourceExpanded(item.id)}
            onCloseSource={onCloseSource!}
            toggleSourceExpansion={toggleSourceExpansion}
            expandable={expandable}
            alwaysExpanded={alwaysExpanded}
            hideArticleIcon={hideArticleIcon}
            maxAbstractLines={maxAbstractLines}
            disableSelectedBorder={disableSelectedBorder}
            disableSelectedHighlight={disableSelectedHighlight}
            iconSpacing={iconSpacing}
            showCloseButton={showCloseButton}
            interventionDetails={interventionDetails}
            outcomeDetails={outcomeDetails}
          />
        </motion.div>
      ))}
    </AnimatePresence>
  );

  if (hideSourceCount) {
    return (
      <Box sx={{ flexGrow: 1, overflowY: 'auto' }}>
        <AnimatePresence initial={false}>
          {currentSources.map((item, index) => (
            <motion.div
              key={item.id}
              style={{
                overflow: 'hidden',
                marginBottom: theme.spacing(1.5),
              }}
              ref={(el) => setCardRef(el, index)}
            >
              <SourceCard
                source={item}
                isSelected={selectedStudy === item.short_paper_id}
                isExpanded={isSourceExpanded(item.id)}
                onCloseSource={onCloseSource!}
                toggleSourceExpansion={toggleSourceExpansion}
                expandable={expandable}
                alwaysExpanded={alwaysExpanded}
                hideArticleIcon={hideArticleIcon}
                maxAbstractLines={maxAbstractLines}
                disableSelectedBorder={disableSelectedBorder}
                disableSelectedHighlight={disableSelectedHighlight}
                iconSpacing={iconSpacing}
                showCloseButton={showCloseButton}
                interventionDetails={interventionDetails}
                outcomeDetails={outcomeDetails}
              />
            </motion.div>
          ))}
        </AnimatePresence>
      </Box>
    )
  }

  const totalCount = sources?.filter(
    (item) =>
      (typeof item.citation === 'string' && item.citation.trim()) ||
      (typeof item.title === 'string' && item.title.trim())
  ).length || 0;
  const filteredCount = currentSources.length;

  return (
    <Card
      elevation={0}
      sx={{
        width: '100%',
        height: '100%',
        border: hideBorder ? 'none' : `1px solid ${theme.palette.divider}`,
        borderRadius: '8px',
        display: 'flex',
        flexDirection: 'column',
        maxHeight: "100%",
        background: theme.palette.common.white,
      }}
    >
      <Grid container
        spacing={0}
        sx={{
          height: '100%',
          flexGrow: 1,
        }}
      >
        {/* Filters Panel Column */}
        {showFiltersPanel && (
          <Grid
            size={{ xs: 12, md: 4 }}
            sx={{
              display: 'flex',
              flexDirection: 'column',
              height: '100%',
              overflow: 'auto',
              borderRight: { xs: 'none', md: `1px solid ${theme.palette.divider}` },
              borderBottom: { xs: `1px solid ${theme.palette.divider}`, md: 'none' },
            }}
          >
            <FilterHeader
              numActiveFilters={activeFiltersCount}
              onClearFilters={handleClearFilters}
              onTogglePanel={handleToggleFiltersPanel}
            />
            <Box
              sx={{ flexGrow: 1, overflow: 'hidden' }}>
              <FiltersPanel
                theme={theme}
                onFiltersChange={onFiltersChange}
                filterOptions={filterOptions}
                activeFilters={activeFilters}
              />
            </Box>
          </Grid>
        )}

        {/* Main Content Column */}
        <Grid
          size={{ xs: 12, md: showFiltersPanel ? 8 : 12 }}
          sx={{
            display: 'flex',
            flexDirection: 'column',
            height: '100%',
            overflow: 'auto',
          }}
        >
          <Box
            sx={{
              p: 2,
              background: theme.palette.common.white,
              zIndex: 1,
              flexShrink: 0,
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              {!showFiltersPanel && (
                <IconButton
                  size="small"
                  sx={{ color: theme.palette.text.secondary, mr: 1 }}
                  onClick={handleToggleFiltersPanel}
                  aria-label="Open filter panel"
                >
                  <FilterListAltIcon />
                </IconButton>
              )}
              {activeFiltersCount > 0 ? (
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    borderRadius: '100px',
                    background: theme.palette.common.white,
                    border: `1px solid ${theme.palette.secondary.main}`,
                    pl: 2,
                    pr: 1,
                    height: '24px',
                    color: theme.palette.primary.main,
                    boxShadow: 'none',
                    gap: 0.5,
                    width: 'max-content',
                    maxWidth: { xs: '100%', md: '200px' },
                  }}
                >
                  <Box
                    component="span"
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      height: '100%',
                      fontFamily: 'Roboto, sans-serif',
                      fontSize: '13px',
                      fontWeight: 400,
                      lineHeight: '18px',
                      letterSpacing: '0.16px',
                    }}
                  >
                    {`${filteredCount} of ${totalCount} sources`}
                  </Box>
                  <IconButton
                    size="small"
                    onClick={handleClearFilters}
                    sx={{
                      ml: 0,
                      p: 0,
                      color: alpha(theme.palette.primary.main, 0.7),
                      height: '16px',
                      width: '16px',
                      minWidth: '16px',
                      minHeight: '16px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                    aria-label="Clear filter"
                  >
                    <CloseIcon sx={{ fontSize: '16px' }} />
                  </IconButton>
                </Box>
              ) : (totalCount > 0 && (
                <Typography
                  variant="body2"
                  sx={{
                    fontSize: 13,
                    fontWeight: 400,
                    color: theme.palette.text.secondary,
                  }}
                >
                  {`${totalCount} sources`}
                </Typography>
              ))}
            </Box>
            {displayCloseButton && (
              <IconButton
                id='test-kush'
                onClick={onClose}
                size="small"
                sx={{
                  color: theme.palette.text.secondary,
                  ml: 0.5,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: '36px',
                  height: '36px',
                  borderRadius: '50%',
                  padding: 0
                }}
              >
                <CloseIcon sx={{ width: 18, height: 18 }} />
              </IconButton>
            )}
          </Box>
          <Box
            id="source-list-scroll"
            sx={{
              overflowY: 'auto',
              flexGrow: 1,
              p: 1.5,
              pt: 1,
            }}
          >
            {renderSourceList()}
          </Box>
        </Grid>
      </Grid>
    </Card>
  );
};

export default Sources;