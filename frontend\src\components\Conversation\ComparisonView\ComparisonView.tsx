import React, { useEffect, useState, memo, useMemo } from "react";
import {
  Box,
  Grid,
  Card,
  IconButton,
} from "@mui/material";
import { Source, FlatEffectSize, FilterState } from "../../../types/ConversationTypes";
import CloseIcon from "@mui/icons-material/Close";
import Sources from "../Sources/Sources";
import "./ComparisonView.css";
import * as d3 from "d3";
import { useIsMobile, useIsTablet } from "../../Layout/MobileUtils";
import EffectSizesPlot2 from "../Plot/Graph/EffectSizesPlot2";
import MeanEffectSizePlot from "../Plot/Graph/MeanEffectSizePlot";
import PairSelector from "../Plot/Graph/PairSelector";
import { PlotDataInfo } from "../../../types/ConversationTypes";
import useScrollbarWidth from "../../../hooks/useScrollbarWidth";
import FiltersPanel from '../FilterPanel/FiltersPanel';
import FilterHeader from '../FilterPanel/FilterHeader';
import FilterListAltIcon from '@mui/icons-material/FilterListAlt';
import ComparisonViewSkeleton from "../../Common/FiltersSkeleton/ComparisonViewSkeleton";

interface ActivePlotDetails {
  citation_ids: { key: string; value: string }[];
  messageId: string;
  plotDataInfo?: {
    data?: PlotDataInfo | { data: PlotDataInfo; type: string };
    title?: string;
  };
}

interface Outcome {
  outcome_tag_id: string | number | null;
  label: string;
}

interface ComparisonViewProps {
  informationId: string;
  sources: Source[] | undefined | null;
  theme: any;
  onClose: () => void;
  activePlotDetails: ActivePlotDetails | null;
  activeSourcePaperIds: string[];
  activeSourceMessageId: string | null;
  selectedStudy: string;
  onSelectStudy?: (id: string) => void;
  onFiltersChange?: (filters: any) => void;
  onResetFilters: () => void;
  filterOptions: FilterState;
  activeFilters: any;
}

const ComparisonView: React.FC<ComparisonViewProps> = ({
  informationId,
  sources,
  theme,
  onClose,
  activePlotDetails,
  activeSourcePaperIds,
  activeSourceMessageId,
  selectedStudy,
  onSelectStudy,
  onFiltersChange,
  onResetFilters,
  filterOptions,
  activeFilters
}) => {
  const [plotLoading, setPlotLoading] = useState(true);
  const [plotError, setPlotError] = useState<string | null>(null);
  const [plotData, setPlotData] = useState<any>({});
  const [showSources, setShowSources] = useState(false);
  const isMobile = useIsMobile();
  const isTablet = useIsTablet();
  const isMobileOrTablet = isMobile || isTablet;
  const [outcomeTagIds, setOutcomeTagIds] = useState('');
  const [interventionTagIds, setInterventionTagIds] = useState('');
  const [flatEffectSizes, setFlatEffectSizes] = useState<FlatEffectSize[]>([]);
  const citationIds = activePlotDetails?.citation_ids || [];
  const messageIdFromPlot = activePlotDetails?.messageId || '';
  const [showFiltersPanel, setShowFiltersPanel] = useState(true);

  const activeFiltersCount = useMemo(() => {
    return Object.values(activeFilters || {}).filter(val => Array.isArray(val) && val.length > 0).length;
  }, [activeFilters]);

  const handleClearFilters = () => {
    onResetFilters();
  };

  const handleToggleFiltersPanel = () => {
    setShowFiltersPanel(prev => !prev);
  };

  const scrollbarWidth = useScrollbarWidth();
  const handleCloseSourceCard = () => {
    if (onSelectStudy) {
      onSelectStudy('');
    }
  };

  function bisectArray<T>(array: T[], fn: (item: T) => boolean): [T[], T[]] {
    return array.reduce(
      (acc: [T[], T[]], val: T) => {
        acc[fn(val) ? 0 : 1].push(val);
        return acc;
      },
      [[], []]
    );
  }

  const topSelection = d3
    .groups(
      flatEffectSizes,
      (d) => `${d.outcome_tag_ids}_${d.intervention_tag_ids}`
    )
    .sort((a, b) => b[1].length - a[1].length);
  const bisected = bisectArray(
    topSelection,
    (d) => d[0] === `${outcomeTagIds}_${interventionTagIds}`
  );
  const top10 = [...bisected[0], ...bisected[1].slice(0, 9)];
  const top10Data = top10.map((d) => d[1]).flat();

  const uniqueOutcomes = top10Data
    .reduce((acc: Outcome[], cur) => {
      const outcomeTagId = cur.outcome_tag_ids;
      if (outcomeTagId != null && String(outcomeTagId) !== '' && cur.outcome_tag_short_labels != null && cur.outcome_tag_short_labels !== '') {
        if (!acc.some((outcome) => String(outcome.outcome_tag_id) === String(outcomeTagId))) {
          acc.push({
            outcome_tag_id: outcomeTagId,
            label: cur.outcome_tag_short_labels,
          });
        }
      }
      return acc;
    }, [])
    .sort((a, b) => a.label.localeCompare(b.label));

  const [selectedOutcome2, setSelectedOutcome2] = useState<Outcome | undefined>(
    uniqueOutcomes.length > 0 ? uniqueOutcomes[0] : undefined
  );

  useEffect(() => {
    if (uniqueOutcomes.length > 0) {
      if (!selectedOutcome2 || !uniqueOutcomes.some(o => String(o.outcome_tag_id) === String(selectedOutcome2.outcome_tag_id))) {
        setSelectedOutcome2(uniqueOutcomes[0]);
      }
    } else if (selectedOutcome2) {
      setSelectedOutcome2(undefined);
    }
  }, [uniqueOutcomes, selectedOutcome2]);

  function getUniqueInterventions(selectedOutcome: Outcome | undefined) {
    if (!selectedOutcome || selectedOutcome.outcome_tag_id == null || String(selectedOutcome.outcome_tag_id) === '') return [];
    const targetOutcomeId = String(selectedOutcome.outcome_tag_id);

    return flatEffectSizes
      .filter((d) =>
        d.outcome_tag_ids != null && typeof d.outcome_tag_ids === 'string' &&
        d.outcome_tag_ids.includes(targetOutcomeId)
      )
      .reduce((acc: any[], cur) => {
        const interventionTagId = cur.intervention_tag_ids;
        if (
          interventionTagId != null && String(interventionTagId) !== '' && cur.intervention_tag_short_labels != null && cur.intervention_tag_short_labels !== '' &&
          !acc.some(
            (intervention) =>
              String(intervention.intervention_tag_id) === String(interventionTagId)
          )
        ) {
          acc.push({
            intervention_tag_id: interventionTagId,
            label: cur.intervention_tag_short_labels,
          });
        }
        return acc;
      }, [])
      .sort((a, b) => a.label.localeCompare(b.label));
  }

  const uniqueInterventions = getUniqueInterventions(selectedOutcome2);

  const handleOnPairClicked = (pair: { intervention: string; outcome: string }) => {
    setInterventionTagIds(pair.intervention);
    setOutcomeTagIds(pair.outcome);
    if (onSelectStudy) {
      onSelectStudy('');
    }
  };

  useEffect(() => {
    if (activePlotDetails && sources && sources.length > 0) {
      const timer = setTimeout(() => {
        setPlotLoading(false);
        const rawPlotData = activePlotDetails.plotDataInfo?.data;
        const resolvedPlotData = (rawPlotData && 'data' in rawPlotData && rawPlotData.data) ? rawPlotData.data : rawPlotData;
        setPlotData(resolvedPlotData);
        const newFlatEffectSizes = (resolvedPlotData?.flat_effect_sizes || []) as FlatEffectSize[];
        setFlatEffectSizes(newFlatEffectSizes);
        if (activePlotDetails.citation_ids?.[0]?.value && activePlotDetails.citation_ids?.[1]?.value) {
          setInterventionTagIds(activePlotDetails.citation_ids[0].value);
          setOutcomeTagIds(activePlotDetails.citation_ids[1].value);
        } else if (newFlatEffectSizes.length > 0) {
          const firstPair = d3
            .groups(
              newFlatEffectSizes,
              (d) => `${d.outcome_tag_ids}_${d.intervention_tag_ids}`
            )
            .sort((a, b) => b[1].length - a[1].length)?.[0]?.[1]?.[0];

          if (firstPair) {
            if (firstPair.intervention_tag_ids != null) setInterventionTagIds(firstPair.intervention_tag_ids);
            if (firstPair.outcome_tag_ids != null) setOutcomeTagIds(firstPair.outcome_tag_ids);
          }
        }
      }, 500);
      return () => clearTimeout(timer);
    } else {
      setPlotLoading(true);
      setPlotData(null);
      setFlatEffectSizes([]);
      setInterventionTagIds('');
      setOutcomeTagIds('');
    }
  }, [activePlotDetails, sources]);

  useEffect(() => {
    if (activePlotDetails && flatEffectSizes.length > 0 && onSelectStudy && !selectedStudy && interventionTagIds && outcomeTagIds) {
      const matchingEffectSizes = flatEffectSizes.filter(
        d => d.intervention_tag_ids != null && typeof d.intervention_tag_ids === 'string' && d.intervention_tag_ids.includes(interventionTagIds) &&
          d.outcome_tag_ids != null && typeof d.outcome_tag_ids === 'string' && d.outcome_tag_ids.includes(outcomeTagIds)
      );
      if (matchingEffectSizes.length > 0) {
        matchingEffectSizes.sort((a, b) => b.hedges_d - a.hedges_d);
        const firstStudyId = String(matchingEffectSizes[0].paper_combined_id || matchingEffectSizes[0].paper_id);
        onSelectStudy(firstStudyId);
      }
    }
  }, [activePlotDetails, flatEffectSizes, onSelectStudy, selectedStudy, interventionTagIds, outcomeTagIds]);

  const getInterventionDetails = () => {
    const matchingEffectSize = flatEffectSizes.find(
      d => d.intervention_tag_ids != null && d.intervention_tag_ids.includes(interventionTagIds) &&
        d.outcome_tag_ids != null && d.outcome_tag_ids.includes(outcomeTagIds) &&
        (String(d.paper_combined_id) === String(selectedStudy) || String(d.paper_id) === String(selectedStudy))
    );

    return {
      name: matchingEffectSize?.intervention_tag_short_labels || 'Unknown Intervention',
      description: matchingEffectSize?.intervention_description || 'No intervention description available'
    };
  };

  const getOutcomeDetails = () => {
    const matchingEffectSize = flatEffectSizes.find(
      d => d.intervention_tag_ids != null && d.intervention_tag_ids.includes(interventionTagIds) &&
        d.outcome_tag_ids != null && d.outcome_tag_ids.includes(outcomeTagIds) &&
        (String(d.paper_combined_id) === String(selectedStudy) || String(d.paper_id) === String(selectedStudy))
    );

    return {
      name: matchingEffectSize?.outcome_tag_short_labels || 'Unknown Outcome',
      description: matchingEffectSize?.outcome_description || 'No outcome description available'
    };
  };

  if (plotLoading || plotError) {
    return (
      <ComparisonViewSkeleton
        onClose={onClose}
        theme={theme}
        hideFiltersPanel={!showFiltersPanel}
      />
    );
  }

  const effectSizes2 =
    flatEffectSizes
      .filter(
        (d) =>
          d.intervention_tag_ids != null && typeof d.intervention_tag_ids === 'string' && d.intervention_tag_ids.includes(interventionTagIds) &&
          d.outcome_tag_ids != null && typeof d.outcome_tag_ids === 'string' && d.outcome_tag_ids.includes(outcomeTagIds)
      )
      ?.sort((a, b) => b.hedges_d - a.hedges_d) || [];

  const xDomain = [
    Math.min(
      0,
      d3.min(effectSizes2, (d) => d.standardized_ci_lower) ?? 0
    ),
    Math.max(
      0,
      d3.max(effectSizes2, (d) => d.standardized_ci_upper) ?? 0
    ),
  ];

  return (
    <Card
      elevation={0}
      sx={{
        maxWidth: "826px",
        width: "100%",
        height: "100%",
        maxHeight: "902px",
        border: `1px solid ${theme.palette.divider}`,
        borderRadius: "8px",
        display: "flex",
        flexDirection: "column",
        background: theme.common.white.main,
      }}
    >
      <Grid container
        spacing={0}
        sx={{
          height: '100%',
          flexGrow: 1,
        }}
      >
        {showFiltersPanel && (
          <Grid
            size={{ xs: 12, md: 4 }}
            sx={{
              display: 'flex',
              flexDirection: 'column',
              height: '100%',
              overflow: 'auto',
              borderRight: { xs: 'none', md: `1px solid ${theme.palette.divider}` },
              borderBottom: { xs: `1px solid ${theme.palette.divider}`, md: 'none' },
            }}
          >
            <FilterHeader
              numActiveFilters={activeFiltersCount}
              onClearFilters={handleClearFilters}
              onTogglePanel={handleToggleFiltersPanel}
            />
            <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
              <FiltersPanel
                theme={theme}
                onFiltersChange={onFiltersChange}
                filterOptions={filterOptions}
                activeFilters={activeFilters}
              />
            </Box>
          </Grid>
        )}

        <Grid
          size={{ xs: 12, md: showFiltersPanel ? 8 : 12 }}
          sx={{
            display: 'flex',
            flexDirection: 'column',
            height: '100%',
            overflow: 'auto',
          }}
        >
          <Box
            id="comparison-view-header"
            sx={{
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "space-between",
              p: 2,
              flexShrink: 0,
            }}
          >
            {!showFiltersPanel && (
              <IconButton
                size="small"
                sx={{ color: theme.palette.text.secondary, mr: 1 }}
                onClick={handleToggleFiltersPanel}
              >
                <FilterListAltIcon />
              </IconButton>
            )}

            <Box sx={{ flexGrow: 1, minWidth: 0 }}>
              <PairSelector
                citationIds={citationIds}
                messageId={messageIdFromPlot}
                plotData={plotData}
                onPairClicked={handleOnPairClicked}
                currentInterventionId={interventionTagIds}
                currentOutcomeId={outcomeTagIds}
              />
            </Box>

            <IconButton
              onClick={onClose}
              size="small"
              sx={{ ml: 0.5, color: theme.palette.text.secondary, width: '36px', height: '36px', borderRadius: '50%', p: 0 }}
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          </Box>
          <Box
            id="comparison-view-content"
            sx={{
              flexGrow: 1,
              display: "flex",
              flexDirection: "column",
              overflow: "hidden",
              gap: 2,
              p: 2,
            }}
          >
            <Box
              sx={{
                height: '43vh',
                minHeight: 0,
                display: 'flex',
                flexDirection: 'column',
                overflow: 'hidden',
                flexShrink: 0,
              }}
            >
              <Grid container spacing={0} sx={{ flexGrow: 1, height: '100%' }}>
                <Grid size={{ xs: 12 }}
                  sx={{ height: '100%' }}>
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      height: '100%',
                      borderRadius: '4px',
                    }}
                  >
                    {plotData &&
                      (Array.isArray(plotData)
                        ? plotData.length > 0
                        : Object.keys(plotData).length > 0) && (
                        <Box
                          id="data-visualization-mean-section-scroll"
                          sx={{
                            flexGrow: 0,
                            flexShrink: 0,
                            display: 'flex',
                            flexDirection: 'column',
                            overflowY: 'overlay',
                            px: `${scrollbarWidth / 2}px`,
                            mb: 1,
                            pt: 1,
                          }}
                        >
                          <MeanEffectSizePlot
                            data={[outcomeTagIds, effectSizes2, interventionTagIds]}
                            xDomain={xDomain}
                          />
                        </Box>
                      )}
                    {plotData &&
                      (Array.isArray(plotData)
                        ? plotData.length > 0
                        : Object.keys(plotData).length > 0) && (
                        <Box
                          id="data-visualization-tab-scroll"
                          sx={{
                            flexGrow: 1,
                            display: 'flex',
                            flexDirection: 'column',
                            p: 0,
                            overflow: 'auto',
                            px: `${scrollbarWidth / 2}px`,
                            pb: 1,
                          }}
                        >
                          <Box
                            sx={{
                              flexGrow: 1,
                              display: 'flex',
                              flexDirection: 'column',
                            }}
                          >
                            <EffectSizesPlot2
                              data={[outcomeTagIds, effectSizes2, interventionTagIds]}
                              xDomain={xDomain}
                              onSelectStudy={onSelectStudy}
                              selectedStudy={selectedStudy}
                            />
                          </Box>
                        </Box>
                      )}
                  </Box>
                </Grid>
              </Grid>
            </Box>

            {(() => {
              if (!selectedStudy || !Array.isArray(sources) || sources.length === 0) {
                return null;
              }

              const selectedSource = sources.find(
                (s) => String(s.short_paper_id) === String(selectedStudy) ||
                  String(s.paper_id) === String(selectedStudy)
              );

              if (!selectedSource) {
                return null;
              }

              return (
                <Box
                  sx={{
                    height: '35vh',
                    border: `1px solid ${theme.palette.divider}`,
                    borderRadius: '4px',
                    backgroundColor: theme.common.white.main,
                    overflowY: 'auto',
                    scrollbarWidth: 'thin',
                    scrollbarColor: '#ABCCFC transparent',
                    '&::-webkit-scrollbar': {
                      width: '8px',
                      background: 'transparent',
                      borderRadius: '8px',
                    },
                    '&::-webkit-scrollbar-thumb': {
                      background: '#ABCCFC',
                      borderRadius: '8px',
                    },
                    '&::-webkit-scrollbar-thumb:hover': {
                      background: '#ABCCFC',
                    },
                    '&::-webkit-scrollbar-corner': {
                      background: 'transparent',
                    },
                    boxSizing: 'border-box',
                  }}
                >
                  {(() => {
                    const interventionDetails = getInterventionDetails();
                    const outcomeDetails = getOutcomeDetails();

                    return (
                      <Sources
                        key={`sources-panel-selected-study-${selectedStudy}`}
                        sources={[selectedSource]}
                        messageId={informationId}
                        selectedStudy={selectedStudy}
                        activeSourcePaperIds={activeSourcePaperIds}
                        activeSourceMessageId={activeSourceMessageId}
                        displayCloseButton={true}
                        onClose={handleCloseSourceCard}
                        expandable={false}
                        alwaysExpanded={true}
                        hideSourceCount={true}
                        hideArticleIcon={true}
                        maxAbstractLines={2}
                        disableSelectedBorder={true}
                        iconSpacing={2}
                        showCloseButton={false}
                        onCloseSource={handleCloseSourceCard}
                        interventionDetails={interventionDetails}
                        outcomeDetails={outcomeDetails}
                        hideBorder={true}
                        isParentLoading={false}
                        onFiltersChange={onFiltersChange}
                        onResetFilters={onResetFilters}
                        filterOptions={filterOptions}
                        activeFilters={activeFilters}
                        mode='comparison'
                      />
                    );
                  })()}
                </Box>
              );
            })()}
            {showSources && (
              <Box
                sx={{
                  flex: isMobileOrTablet ? "0 0 184px" : "0 0 22%",
                  overflowY: "auto",
                  display: "flex",
                  flexDirection: "column",
                  borderRadius: "8px",
                  p: 0,
                }}
              >
                <Sources
                  key={`sources-panel-${informationId}`}
                  onClose={() => {
                    setShowSources(false);
                  }}
                  sources={sources}
                  messageId={informationId}
                  selectedStudy=""
                  activeSourcePaperIds={[]}
                  activeSourceMessageId={null}
                  displayCloseButton={true}
                  maxAbstractLines={3}
                  disableAutoScroll={true}
                  disableSelectedBorder={true}
                  disableSelectedHighlight={true}
                  onFiltersChange={onFiltersChange}
                  onResetFilters={onResetFilters}
                  filterOptions={filterOptions}
                  activeFilters={activeFilters}
                />
              </Box>
            )}
          </Box>
        </Grid>
      </Grid>
    </Card>
  );
};

export default memo(ComparisonView);