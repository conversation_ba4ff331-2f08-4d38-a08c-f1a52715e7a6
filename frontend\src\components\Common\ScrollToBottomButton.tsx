import React from 'react';
import { IconButton, Zoom, useTheme } from '@mui/material';
import { ScrollToBottomIcon } from './Icons';

interface ScrollToBottomButtonProps {
  show: boolean;
  onClick: () => void;
  isStreaming?: boolean;
  className?: string;
}

const ScrollToBottomButton: React.FC<ScrollToBottomButtonProps> = ({
  show,
  onClick,
  isStreaming = false,
  className = ''
}) => {
  const theme = useTheme();

  return (
    <Zoom in={show} timeout={200}>
      <IconButton
        onClick={onClick}
        className={className}
        sx={{
          position: 'fixed',
          bottom: 40,
          left: '50%',
          transform: 'translateX(-50%)',
          zIndex: 1000,
          width: 32,
          height: 32,
          padding: 0,
          backgroundColor: 'white',
          border: 'none',
          borderRadius: '50%',
          boxShadow: theme.shadows[2],
          '&:hover': {
            backgroundColor: 'white',
            boxShadow: theme.shadows[4],
          },
          '&:active': {
            boxShadow: theme.shadows[1],
          },
          ...(isStreaming && {
            animation: 'pulse 2s infinite',
            '@keyframes pulse': {
              '0%': {
                boxShadow: `0 0 0 0 ${theme.components?.icon?.defaultLight || theme.palette.primary.main}40`,
              },
              '70%': {
                boxShadow: `0 0 0 10px ${theme.components?.icon?.defaultLight || theme.palette.primary.main}00`,
              },
              '100%': {
                boxShadow: `0 0 0 0 ${theme.components?.icon?.defaultLight || theme.palette.primary.main}00`,
              },
            },
          }),
        }}
        aria-label={isStreaming ? "Scroll to see new content" : "Scroll to bottom"}
      >
        <ScrollToBottomIcon
          sx={{
            width: 32,
            height: 32
          }}
        />
      </IconButton>
    </Zoom>
  );
};

export default ScrollToBottomButton;
