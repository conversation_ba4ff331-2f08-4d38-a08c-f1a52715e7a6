import React from 'react';
import { Fab, Zoom, useTheme } from '@mui/material';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';

interface ScrollToBottomButtonProps {
  show: boolean;
  onClick: () => void;
  isStreaming?: boolean;
  className?: string;
}

const ScrollToBottomButton: React.FC<ScrollToBottomButtonProps> = ({
  show,
  onClick,
  isStreaming = false,
  className = ''
}) => {
  const theme = useTheme();

  return (
    <Zoom in={show} timeout={200}>
      <Fab
        size="small"
        onClick={onClick}
        className={className}
        sx={{
          position: 'fixed',
          bottom: 120,
          right: 32,
          zIndex: 1000,
          backgroundColor: theme.palette.background.paper,
          color: theme.palette.text.primary,
          border: `1px solid ${theme.palette.divider}`,
          boxShadow: theme.shadows[4],
          '&:hover': {
            backgroundColor: theme.palette.action.hover,
            boxShadow: theme.shadows[8],
          },
          '&:active': {
            boxShadow: theme.shadows[2],
          },
          ...(isStreaming && {
            animation: 'pulse 2s infinite',
            '@keyframes pulse': {
              '0%': {
                boxShadow: `0 0 0 0 ${theme.palette.primary.main}40`,
              },
              '70%': {
                boxShadow: `0 0 0 10px ${theme.palette.primary.main}00`,
              },
              '100%': {
                boxShadow: `0 0 0 0 ${theme.palette.primary.main}00`,
              },
            },
          }),
        }}
        aria-label={isStreaming ? "Scroll to see new content" : "Scroll to bottom"}
      >
        <KeyboardArrowDownIcon />
      </Fab>
    </Zoom>
  );
};

export default ScrollToBottomButton;
