import { useRef, useEffect, useContext } from 'react';
import { LayoutContext } from '../components/Layout/LayoutContext';

interface UseChatScrollProps {
  scrollContainerRef: React.RefObject<HTMLDivElement | null>;
  displaySystemLoader: boolean;
  forceScrollToEnd: boolean;
  onForceScrollComplete: () => void;
  disableAutoScrollDuringStreaming?: boolean;
}

const useChatScroll = ({
  scrollContainerRef,
  displaySystemLoader,
  forceScrollToEnd,
  onForceScrollComplete,
  disableAutoScrollDuringStreaming = false
}: UseChatScrollProps) => {
  const userScrolled = useRef(false);
  const observerRef = useRef<MutationObserver | null>(null);
  const ignoreNextScroll = useRef(false);
  const { isStreamingContent, updateIsStreamingContent } = useContext(LayoutContext);

  useEffect(() => {
    const target = scrollContainerRef.current;

    const resetUserScrolled = () => {
      userScrolled.current = false;
    };

    const handleScroll = () => {
      if (ignoreNextScroll.current) {
        ignoreNextScroll.current = false;
        return;
      }

      const wasUserScrolled = userScrolled.current;
      userScrolled.current = true;

      if (observerRef.current && !(isStreamingContent || displaySystemLoader || forceScrollToEnd)) {
        observerRef.current.disconnect();
        observerRef.current = null;
      }

      if (isStreamingContent && !wasUserScrolled && !displaySystemLoader) {
        updateIsStreamingContent(false);
      }
    };

    if (target) {
      target.addEventListener('scroll', handleScroll);

      if (isStreamingContent || displaySystemLoader || forceScrollToEnd) {
        resetUserScrolled();

        if (observerRef.current) {
          observerRef.current.disconnect();
        }

        const observer = new MutationObserver((mutationsList) => {
          if (!userScrolled.current && (isStreamingContent || displaySystemLoader || forceScrollToEnd)) {
            for (const mutation of mutationsList) {
              if (mutation.type === 'childList' || mutation.type === 'characterData' || mutation.type === 'attributes') {
                if (target) {
                  requestAnimationFrame(() => {
                    ignoreNextScroll.current = true;
                    if (!disableAutoScrollDuringStreaming || forceScrollToEnd || displaySystemLoader) {
                      target.scrollTop = target.scrollHeight;
                    }
                    if (forceScrollToEnd) {
                      onForceScrollComplete();
                    }
                  });
                }
                break;
              }
            }
          }
        });

        observer.observe(target, { childList: true, subtree: true, characterData: true, attributes: true });
        observerRef.current = observer;
      } else {
        if (observerRef.current) {
          observerRef.current.disconnect();
          observerRef.current = null;
        }
      }

      return () => {
        target.removeEventListener('scroll', handleScroll);
        if (observerRef.current) {
          observerRef.current.disconnect();
          observerRef.current = null;
        }
      };
    }

    return () => {
      if (target) {
        target.removeEventListener('scroll', handleScroll);
      }
      if (observerRef.current) {
        observerRef.current.disconnect();
        observerRef.current = null;
      }
    };
  }, [isStreamingContent, scrollContainerRef, displaySystemLoader, forceScrollToEnd, onForceScrollComplete, disableAutoScrollDuringStreaming]);
};

export default useChatScroll;