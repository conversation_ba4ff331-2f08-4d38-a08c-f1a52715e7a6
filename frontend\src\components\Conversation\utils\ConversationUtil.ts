import { PlotDataInfo } from "../../../types/ConversationTypes";

export const extractFilterOptionsFromData = (data: PlotDataInfo) => {
    const years = new Set<number>();
    const sectors = new Set<string>();
    const countries = new Set<string>();
    const regions = new Set<string>();
    const incomeGroups = new Set<string>();
    const qualityScoreCategories = new Set<string>();

    if (data?.flat_effect_sizes) {
        data.flat_effect_sizes.forEach(plotItem => {
            if (plotItem.year) years.add(plotItem.year);

            if (plotItem.intervention_sectors) {
                const sectorArray = plotItem.intervention_sectors.split(';');
                sectorArray.forEach(sector => {
                    sectors.add(sector.trim());
                });
            }

            if (plotItem.outcome_sector) {
                const sectorArray = plotItem.outcome_sector.split(';');
                sectorArray.forEach(sector => {
                    sectors.add(sector.trim());
                });
            }

            if (plotItem.country_name) countries.add(plotItem.country_name);
            if (plotItem.region) regions.add(plotItem.region);
            if (plotItem.income_group) incomeGroups.add(plotItem.income_group);

            // quality_score_category is a single value, so no splitting is needed
            if (plotItem.quality_score_category) {
                qualityScoreCategories.add(plotItem.quality_score_category.trim());
            }
        });
    }

    return {
        years: Array.from(years).sort((a, b) => a - b),
        sectors: Array.from(sectors).sort(),
        countries: Array.from(countries).sort(),
        regions: Array.from(regions).sort(),
        incomeGroups: Array.from(incomeGroups).sort(),
        qualityScoreCategories: Array.from(qualityScoreCategories).sort()
    };
};

export const resolvePlotData = (rawPlotData: any): PlotDataInfo | null => {
    if (rawPlotData && rawPlotData.data && rawPlotData.data.data) {
        return rawPlotData.data.data;
    } else if (rawPlotData && rawPlotData.data) {
        return rawPlotData.data;
    } else if (rawPlotData && rawPlotData.flat_effect_sizes) {
        return rawPlotData;
    }
    return null;
};