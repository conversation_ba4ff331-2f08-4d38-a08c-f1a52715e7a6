html {
    scroll-behavior: smooth;
}

.active-message {
    min-height: 84px;
}

.conversation-wrapper,
.conversation-wrapper-inner {
    overflow-anchor: none;
}

.conversation-single-column-scroll-box {
    -webkit-overflow-scrolling: touch;
}

/* Enhanced streaming behavior - prevent scroll anchoring during content updates */
.conversation-wrapper.streaming,
.conversation-wrapper-inner.streaming {
    overflow-anchor: none;
    scroll-behavior: auto; /* Disable smooth scrolling during streaming for better performance */
}

/* Restore smooth scrolling when not streaming */
.conversation-wrapper:not(.streaming),
.conversation-wrapper-inner:not(.streaming) {
    scroll-behavior: smooth;
}