import React, { useCallback, useContext, useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useConversationData } from "../../hooks/useConversationData";
import { post } from "../../services/apiService";
import { ConversationNewMessagedResponse, Message } from "../../types/ConversationTypes";
import { LayoutContext } from "../Layout/LayoutContext";
import './Conversation.css';
import DisplayConversation from "./DisplayConversations";
import CenteredLandingPage from "./LandingPage/CenteredLandingPage";
import useDynamicMeta from '../../hooks/useDynamicMeta';

type UserPromptType = {
  message: string | null;
};

type InitResponse = {
  success: boolean;
  data: {
    user_id: string;
    conversation_id: string;
  };
};

const Conversation: React.FC = () => {
  const { conversationId: paramConversationId } = useParams<{ conversationId: string }>();
  const navigate = useNavigate();
  const [informationMessageId, setInformationMessageId] = useState<string | null>(null);
  const [displaySystemLoader, setDisplaySystemLoader] = useState(false);
  const [pendingPrompt, setPendingPrompt] = useState<UserPromptType | null>(null);
  const [newQuestionId, setNewQuestionId] = useState<string | null>(null);
  const [streamingEnded, setStreamingEnded] = useState<boolean | null>(false);
  const [summaryStreamedText, setSummaryStreamedText] = useState<string | null>("");
  const [localMessageList, setLocalMessageList] = useState<Message[]>([]);
  const [forceScrollToEnd, setForceScrollToEnd] = useState(false);

  useDynamicMeta({
    title: 'ImpactAI - Tranform Evidence into Action - World Bank Group',
    description: 'ImpactAI is revolutionizing global development and empowering development practitioners with a GenAI-powered tool, delivering causal research insights in seconds.'
  });

  const {
    appLoader,
    updateErrorMessageState,
    isStreamingContent,
    updateIsStreamingContent,
    updateConversationId,
    refreshChatHistory,
    streamingError,
    updateStreamingErrorState,
    conversationId: contextConversationId,
    updateRefreshChatHistory,
  } = useContext(LayoutContext);

  const {
    messages: fetchedMessages,
    options,
    errorLoadingMessages,
    isSingleSystemSuggestedTopic,
  } = useConversationData(
    paramConversationId || "",
    informationMessageId,
    localMessageList,
    summaryStreamedText,
    refreshChatHistory,
    streamingError
  );
  const resetAllStreamingIdStates = useCallback(() => {
    setStreamingEnded(false);
    setNewQuestionId(null);
    setInformationMessageId('');
    setSummaryStreamedText('');
  }, []);

  useEffect(() => {
    const isInitialBlankPage = !contextConversationId && !informationMessageId && !displaySystemLoader;
    const shouldRefreshForNewChatCreation = informationMessageId && !!paramConversationId;
    if (isInitialBlankPage || shouldRefreshForNewChatCreation) {
      updateRefreshChatHistory(true);
    }
  }, [informationMessageId, paramConversationId, displaySystemLoader, contextConversationId]);

  useEffect(() => {
    if (errorLoadingMessages) {
      resetAllStreamingIdStates();
      setDisplaySystemLoader(false);
      updateErrorMessageState(errorLoadingMessages);
    }
  }, [errorLoadingMessages, resetAllStreamingIdStates, updateErrorMessageState]);

  const initialiseNewConversation = useCallback(async (): Promise<string | null> => {
    try {
      const response: InitResponse = await post('/conversations/initialise');
      if (response.success && response.data.conversation_id) {
        return response.data.conversation_id;
      } else {
        throw new Error("Failed to initialize conversation.");
      }
    } catch (error: any) {
      updateErrorMessageState(error.message || "Error initializing chat.");
      return null;
    }
  }, [updateErrorMessageState]);

  useEffect(() => {
    if (streamingEnded) {
      setForceScrollToEnd(true);
      setLocalMessageList(prevMessages => {
        const updatedMessages = [...prevMessages];
        const lastMessageIndex = updatedMessages.length - 1;
        if (updatedMessages[lastMessageIndex]?.author === 'system' && updatedMessages[lastMessageIndex]?.type === 'information' && updatedMessages[lastMessageIndex]?.id === 'system-loading') {
          updatedMessages[lastMessageIndex] = {
            ...updatedMessages[lastMessageIndex],
            id: informationMessageId,
          };
        }
        return updatedMessages;
      });
      setStreamingEnded(false);
      setNewQuestionId(null);
      setInformationMessageId('');
      setForceScrollToEnd(true);
    }
  }, [streamingEnded]);

  useEffect(() => {
    const isNewConversationPending = sessionStorage.getItem('isNewConversationPending');
    const pendingMessageText = sessionStorage.getItem('pendingNewConversationMessage');
    if (paramConversationId && fetchedMessages && fetchedMessages.length > 0) {
      setLocalMessageList(fetchedMessages);
    } else if (!paramConversationId && !(isNewConversationPending === 'true' && pendingMessageText)) {
      setLocalMessageList([]);
    }
    if (isNewConversationPending === 'true' && pendingMessageText && paramConversationId) {
      sessionStorage.removeItem('isNewConversationPending');
      sessionStorage.removeItem('pendingNewConversationMessage');
    }
  }, [paramConversationId, fetchedMessages, isSingleSystemSuggestedTopic]);

  const handleScrollComplete = async () => {
    if (!paramConversationId) {
      console.warn("handleScrollComplete called without paramConversationId. Skipping.");
      return;
    }
    setDisplaySystemLoader(true);
    try {
      const messageToSend = pendingPrompt?.message || "";
      const response: ConversationNewMessagedResponse = await post(`/conversations/${paramConversationId}`, { message: messageToSend });
      if (response.success) {
        const actualInformationMessageId = response.data.information_message_id;
        if (actualInformationMessageId) {
          setInformationMessageId(actualInformationMessageId);
        } else {
          setDisplaySystemLoader(false);
          updateIsStreamingContent(false);
          updateErrorMessageState("No response received.");
        }
        const hasSingleAnswerFromUser = localMessageList.filter(
          (msg) => msg.type === 'answer' && msg.author === 'user'
        ).length === 1;
        if (paramConversationId && hasSingleAnswerFromUser) {
          if (paramConversationId === contextConversationId) {
            updateRefreshChatHistory(true);
          }
        }
      } else {
        setDisplaySystemLoader(false);
        updateIsStreamingContent(false);
        updateErrorMessageState(response.error || "Failed to send message.");
      }
    } catch (error: any) {
      setDisplaySystemLoader(false);
      updateIsStreamingContent(false);
      updateErrorMessageState(error.message || "An unexpected error occurred.");
    }
  };

  const handleUserPromptAndInitialise = async (newMessage: any) => {
    setDisplaySystemLoader(true);
    updateIsStreamingContent(true);
    updateStreamingErrorState(false);
    sessionStorage.removeItem('isNewConversationPending');
    sessionStorage.removeItem('pendingNewConversationMessage');
    const messageText = newMessage?.value || newMessage;
    let currentConversationId = paramConversationId;
    const newQuestionId = 'user-loading-' + Date.now();
    const newSystemMessageId = 'system-loading';
    const newQuestion: Message = {
      author: 'user',
      created_at: '',
      id: newQuestionId,
      text: messageText,
      type: 'answer',
      choices: {
        type: "loading",
        options: []
      }
    };
    const newSystemMessage: Message = {
      type: 'information',
      author: 'system',
      id: newSystemMessageId,
      conversation_id: currentConversationId || null,
      text: '',
    };
    setLocalMessageList(prevMessages => [...prevMessages, newQuestion, newSystemMessage]);
    resetAllStreamingIdStates();
    setNewQuestionId(newQuestionId);
    setPendingPrompt({ message: messageText });
    if (!currentConversationId) {
      sessionStorage.setItem('pendingNewConversationMessage', messageText);
      sessionStorage.setItem('isNewConversationPending', 'true');
      currentConversationId = await initialiseNewConversation();
      if (!currentConversationId) {
        setDisplaySystemLoader(false);
        updateIsStreamingContent(false);
        resetAllStreamingIdStates();
        sessionStorage.removeItem('pendingNewConversationMessage');
        sessionStorage.removeItem('isNewConversationPending');
        setLocalMessageList(prevMessages => prevMessages.filter(
          msg => msg.id !== newQuestionId && msg.id !== newSystemMessageId
        ));
        return;
      }
      updateConversationId(currentConversationId);
      navigate(`/${currentConversationId}`, { replace: true });
    }
  };

  if (!paramConversationId && localMessageList.length === 0) {
    return (
      <CenteredLandingPage
        handleUserPrompt={handleUserPromptAndInitialise}
      />
    );
  }

  return (
    <DisplayConversation
      loadingMessages={appLoader}
      localConversationId={paramConversationId}
      displaySystemLoader={displaySystemLoader}
      setDisplaySystemLoader={setDisplaySystemLoader}
      streamingEnded={streamingEnded}
      setStreamingEnded={setStreamingEnded}
      handleUserPrompt={handleUserPromptAndInitialise}
      informationMessageId={informationMessageId}
      onScrollComplete={handleScrollComplete}
      newQuestionId={newQuestionId}
      initialConversationMessages={localMessageList}
      summaryStreamedText={summaryStreamedText}
      setSummaryStreamedText={setSummaryStreamedText}
      setLocalMessageList={setLocalMessageList}
      forceScrollToEnd={forceScrollToEnd}
      onForceScrollComplete={() => setForceScrollToEnd(false)}
    />
  );
};

export default Conversation;